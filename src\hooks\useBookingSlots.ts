import { useBookingPageDetailStore } from '@/modules/admin/stores/booking-page-detail.store'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import { format } from 'date-fns'
import { useCallback, useEffect } from 'react'

/**
 * Custom hook for managing booking slots
 * Provides convenient methods for loading and checking slot availability
 */
export const useBookingSlots = () => {
  const bookingPage = useBookingPageDetailStore(state => state.bookingPage)
  const {
    bookedSlots,
    selectedDate,
    isLoading,
    error,
    loadBookedSlots,
    setBookingPageId,
    isSlotBooked,
    getSlotStatus,
    clearBookedSlots,
  } = useBookingSlotsStore()

  // Initialize booking page ID when available
  useEffect(() => {
    if (bookingPage?.id) {
      setBookingPageId(bookingPage.id)
    }
  }, [bookingPage?.id, setBookingPageId])

  // Load slots for a specific date
  const loadSlotsForDate = useCallback(
    async (date: Date) => {
      if (!bookingPage?.id) {
        console.warn('No booking page ID available')
        return
      }

      const dateString = format(date, 'yyyy-MM-dd')
      await loadBookedSlots(bookingPage.id, dateString)
    },
    [bookingPage?.id, loadBookedSlots],
  )

  // Check if a slot is available (not booked or cancelled)
  const isSlotAvailable = useCallback(
    (field: string, time: string) => {
      return getSlotStatus(field, time) === 'available'
    },
    [getSlotStatus],
  )

  // Get all booked slots for a specific field
  const getBookedSlotsForField = useCallback(
    (field: string) => {
      return bookedSlots.filter(slot => slot.field === field)
    },
    [bookedSlots],
  )

  // Get all booked slots for a specific time
  const getBookedSlotsForTime = useCallback(
    (time: string) => {
      return bookedSlots.filter(slot => slot.time === time)
    },
    [bookedSlots],
  )

  // Get booking statistics
  const getBookingStats = useCallback(() => {
    const total = bookedSlots.length
    const pending = bookedSlots.filter(slot => slot.status === 'pending').length
    const confirmed = bookedSlots.filter(slot => slot.status === 'confirmed').length
    const cancelled = bookedSlots.filter(slot => slot.status === 'cancelled').length

    return {
      total,
      pending,
      confirmed,
      cancelled,
      available: total - cancelled, // Available = total - cancelled
    }
  }, [bookedSlots])

  return {
    // State
    bookedSlots,
    selectedDate,
    isLoading,
    error,
    bookingPageId: bookingPage?.id,

    // Actions
    loadSlotsForDate,
    clearBookedSlots,

    // Checkers
    isSlotBooked,
    isSlotAvailable,
    getSlotStatus,

    // Getters
    getBookedSlotsForField,
    getBookedSlotsForTime,
    getBookingStats,
  }
}
